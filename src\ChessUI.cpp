#include "ChessUI.h"
#include <iostream>
#include <algorithm>
#include <cctype>

ChessUI::ChessUI() : engine_(Color::BLACK, 4), humanIsWhite_(true) {}

void ChessUI::run()
{
    displayWelcome();
    setupGame();

    while (game_.getGameState() == GameState::PLAYING ||
           game_.getGameState() == GameState::CHECK)
    {

        displayBoard();
        displayGameStatus();

        if ((game_.getCurrentPlayer() == Color::WHITE && humanIsWhite_) ||
            (game_.getCurrentPlayer() == Color::BLACK && !humanIsWhite_))
        {
            playHumanTurn();
        }
        else
        {
            playComputerTurn();
        }
    }

    displayBoard();
    displayGameOver();
}

void ChessUI::setupGame()
{
    humanIsWhite_ = askHumanColor();
    engine_.setColor(humanIsWhite_ ? Color::BLACK : Color::WHITE);
    game_.startNewGame();

    std::cout << "\nGame setup complete!\n";
    std::cout << "You are playing as " << (humanIsWhite_ ? "White" : "Black") << "\n";
    std::cout << "Enter moves in algebraic notation (e.g., 'e2e4')\n";
    std::cout << "Type 'help' for more commands.\n\n";
}

bool ChessUI::askHumanColor()
{
    std::string input;
    while (true)
    {
        std::cout << "Do you want to play as White or Black? (w/b): ";
        std::getline(std::cin, input);
        input = toLowerCase(input);

        if (input == "w" || input == "white")
        {
            return true;
        }
        else if (input == "b" || input == "black")
        {
            return false;
        }
        else
        {
            std::cout << "Please enter 'w' for White or 'b' for Black.\n";
        }
    }
}

void ChessUI::playHumanTurn()
{
    while (true)
    {
        std::string input = getPlayerInput();

        if (processCommand(input))
        {
            continue; // Command was processed, get next input
        }

        Move move = parseMove(input);
        if (move.isValid())
        {
            bool success = game_.makeMove(move);
            displayMoveResult(success, input);
            if (success)
            {
                break; // Valid move made, end turn
            }
        }
        else
        {
            std::cout << "Invalid move format. Use algebraic notation (e.g., 'e2e4')\n";
        }
    }
}

void ChessUI::playComputerTurn()
{
    std::cout << "Computer is thinking...\n";

    Move computerMove = engine_.getBestMove(game_);
    if (computerMove.isValid())
    {
        bool success = game_.makeMove(computerMove);
        if (success)
        {
            std::cout << "Computer plays: " << computerMove.toAlgebraic() << "\n\n";
        }
        else
        {
            std::cout << "Error: Computer made invalid move!\n";
        }
    }
    else
    {
        std::cout << "Computer has no valid moves!\n";
    }
}

std::string ChessUI::getPlayerInput()
{
    std::string input;
    std::cout << colorToString(game_.getCurrentPlayer()) << " to move: ";
    std::getline(std::cin, input);
    return input;
}

Move ChessUI::parseMove(const std::string &input)
{
    std::string cleanInput = toLowerCase(input);

    // Remove spaces
    cleanInput.erase(std::remove(cleanInput.begin(), cleanInput.end(), ' '), cleanInput.end());

    return Move::fromAlgebraic(cleanInput);
}

bool ChessUI::processCommand(const std::string &input)
{
    std::string command = toLowerCase(input);

    if (command == "help" || command == "h")
    {
        displayHelp();
        return true;
    }
    else if (command == "board" || command == "b")
    {
        displayBoard();
        return true;
    }
    else if (command == "status" || command == "s")
    {
        displayGameStatus();
        return true;
    }
    else if (command == "quit" || command == "q" || command == "exit")
    {
        std::cout << "Thanks for playing!\n";
        exit(0);
    }

    return false; // Not a command
}

void ChessUI::displayWelcome()
{
    clearScreen();
    std::cout << "========================================\n";
    std::cout << "         WELCOME TO CHESS ENGINE       \n";
    std::cout << "========================================\n\n";
    std::cout << "A complete chess engine with AI opponent\n";
    std::cout << "Supports all chess rules including castling,\n";
    std::cout << "en passant, and pawn promotion.\n\n";
}

void ChessUI::displayHelp()
{
    std::cout << "\n========== HELP ==========\n";
    std::cout << "Move format: Use algebraic notation\n";
    std::cout << "  Examples: e2e4, g1f3, e7e8q (pawn promotion)\n\n";
    std::cout << "Commands:\n";
    std::cout << "  help, h     - Show this help\n";
    std::cout << "  board, b    - Display current board\n";
    std::cout << "  status, s   - Show game status\n";
    std::cout << "  quit, q     - Exit the game\n\n";
    std::cout << "Special moves:\n";
    std::cout << "  Castling: Move king two squares (e1g1, e1c1)\n";
    std::cout << "  En passant: Capture pawn that just moved two squares\n";
    std::cout << "  Promotion: Add piece letter at end (e7e8q for queen)\n";
    std::cout << "    q=Queen, r=Rook, b=Bishop, n=Knight\n";
    std::cout << "==========================\n\n";
}

void ChessUI::displayBoard()
{
    std::cout << game_.getBoardString() << std::endl;
}

void ChessUI::displayGameStatus()
{
    std::cout << game_.getGameStatusString() << "\n";

    if (!game_.getMoveHistory().empty())
    {
        std::cout << "Last move: " << game_.getLastMoveString() << "\n";
    }

    std::cout << std::endl;
}

void ChessUI::displayMoveResult(bool success, const std::string &move)
{
    if (success)
    {
        std::cout << "Move " << move << " played successfully!\n\n";
    }
    else
    {
        std::cout << "Invalid move: " << move << "\n";
        std::cout << "Please try again.\n\n";
    }
}

void ChessUI::displayGameOver()
{
    std::cout << "\n========== GAME OVER ==========\n";
    std::cout << game_.getGameStatusString() << "\n";

    switch (game_.getGameState())
    {
    case GameState::CHECKMATE:
        if ((game_.getCurrentPlayer() == Color::WHITE && !humanIsWhite_) ||
            (game_.getCurrentPlayer() == Color::BLACK && humanIsWhite_))
        {
            std::cout << "Congratulations! You won!\n";
        }
        else
        {
            std::cout << "Computer wins! Better luck next time.\n";
        }
        break;
    case GameState::STALEMATE:
    case GameState::DRAW:
        std::cout << "The game ended in a draw.\n";
        break;
    default:
        break;
    }

    std::cout << "===============================\n\n";
    std::cout << "Thanks for playing!\n";
}

void ChessUI::clearScreen()
{
// Cross-platform screen clearing
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void ChessUI::waitForEnter()
{
    std::cout << "Press Enter to continue...";
    std::cin.ignore();
}

std::string ChessUI::toLowerCase(const std::string &str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}
