#pragma once

#include "ChessGame.h"
#include "ChessEngine.h"
#include <string>

class ChessUI {
private:
    ChessGame game_;
    ChessEngine engine_;
    bool humanIsWhite_;
    
public:
    ChessUI();
    
    // Main game loop
    void run();
    
    // Game setup
    void setupGame();
    bool askHumanColor();
    
    // Input/Output
    std::string getPlayerInput();
    Move parseMove(const std::string& input);
    void displayWelcome();
    void displayHelp();
    void displayBoard();
    void displayGameStatus();
    void displayMoveResult(bool success, const std::string& move);
    void displayGameOver();
    
    // Game flow
    void playHumanTurn();
    void playComputerTurn();
    bool processCommand(const std::string& input);
    
    // Utility
    void clearScreen();
    void waitForEnter();
    std::string toLowerCase(const std::string& str);
};
