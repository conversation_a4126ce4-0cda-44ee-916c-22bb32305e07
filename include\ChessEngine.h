#pragma once

#include "ChessGame.h"
#include "Move.h"
#include <limits>

class ChessEngine {
private:
    int maxDepth_;
    Color engineColor_;
    
    // Piece-square tables for position evaluation
    static const int PAWN_TABLE[8][8];
    static const int KNIGHT_TABLE[8][8];
    static const int BISHOP_TABLE[8][8];
    static const int ROOK_TABLE[8][8];
    static const int QUEEN_TABLE[8][8];
    static const int KING_MIDDLE_GAME_TABLE[8][8];
    static const int KING_END_GAME_TABLE[8][8];
    
public:
    ChessEngine(Color color = Color::BLACK, int depth = 4);
    
    // Main AI interface
    Move getBestMove(const ChessGame& game);
    
    // Evaluation functions
    int evaluatePosition(const ChessBoard& board, Color perspective) const;
    int evaluatePiece(const Piece* piece, const Position& pos, bool isEndGame) const;
    
    // Minimax with alpha-beta pruning
    int minimax(ChessGame& game, int depth, int alpha, int beta, bool maximizingPlayer);
    
    // Utility functions
    void setDepth(int depth) { maxDepth_ = depth; }
    void setColor(Color color) { engineColor_ = color; }
    Color getColor() const { return engineColor_; }
    
private:
    bool isEndGame(const ChessBoard& board) const;
    int getPieceSquareValue(PieceType type, const Position& pos, Color color, bool isEndGame) const;
    std::vector<Move> orderMoves(const std::vector<Move>& moves, const ChessGame& game) const;
};
